/*******************************************************************************
* @Copyright (C) 2023 by Vantage Elevation
* @file           : flash_handler.c
* @version 		  : 1.0.2
* @brief Handle the Flash functionality
* @details Handle the Flash functionality
*****************************************************************************
* @version 1.0.1                                Date : 24/07/2025
* [+] Added Start FW file transfer
* Modified send_data_to_nexus function
********************************************************************************************
* @version 1.0.2                                        				Date : 26/07/2025
* Modified for Data Offset Approach
*****************************************************************************/
#include "flash_handler.h"
#include "esp_log.h"
#include "utility.h"
#include "uart_handler.h"
#include "ble_response_handler.h"
#include "esp_timer.h"
#include "crc.h"
#include <string.h>
#include <sys/stat.h>

static esp_vfs_littlefs_conf_t config;

#define BUF_SIZE            (1024)
#define CHUNK_SIZE          (256 * BUF_SIZE)

extern uint8_t write_file_buffer[CHUNK_SIZE];
extern volatile bool ack_flag;
extern volatile bool nack_flag;
QueueHandle_t file_event_queue;

const char* basepath = "/storage";
const char* filepath = "/storage/nexus_fw.bin";
static uint32_t current_file_length = 0;

/**
 * @brief Initializes the flash memory subsystem.
 *
 * This function sets up the flash memory interface for read, write, and erase operations.
 * It may configure controller registers, perform sanity checks, or unlock access
 * as required by the specific platform or driver.
 *
 * @return int32_t Returns 0 on success, or a negative error code on failure.
 */
int32_t flash_initalization(void) {   
    config.base_path = basepath;
    config.format_if_mount_failed = true;
    config.partition_label = "storage";
    config.dont_mount = false;

    esp_err_t result = esp_vfs_littlefs_register(&config);
    ESP_LOGI("FLASH: ", "%d", result);

    if (result == ESP_OK) {
        ESP_LOGI("FLASH: ", "LittleFS mounted successfully at %s", config.base_path);
    } else if (result == ESP_FAIL) {
        ESP_LOGE("FLASH: ", "Failed to mount or format filesystem");
    } else if (result == ESP_ERR_NOT_FOUND) {
        ESP_LOGE("FLASH: ", "Failed to find LittleFS partition");
    } else {
        ESP_LOGE("FLASH: ", "Failed with error: %s", esp_err_to_name(result));
    }
    return (int32_t)result;
}

/**
 * @brief Retrieves total and used flash memory size.
 *
 * This function calculates or queries the total flash memory size and the amount
 * currently used. It writes the values to the memory locations pointed to by
 * the `total` and `used` pointers.
 *
 * @param[out] total  Pointer to a variable where the total flash size (in bytes) will be stored.
 * @param[out] used   Pointer to a variable where the used flash size (in bytes) will be stored.
 *
 * @return uint32_t   Returns 0 on success, or a non-zero error code on failure.
 */
uint32_t get_total_flash_size(uint32_t* total, uint32_t* used) {
    size_t* l_total = (size_t*)total;
    size_t* l_used = (size_t*)used;
    esp_err_t result = esp_littlefs_info(config.partition_label, l_total, l_used);
    *total = *l_total;
    *used = *l_used;
    // ESP_LOGI("FLASH SIZE: ", "Total: %ld, Used: %ld", *total, *used);

    return (int32_t)result;
}

/**
 * @brief Writes a block of data to flash memory.
 *
 * This function writes the specified data buffer to flash memory. It ensures proper alignment,
 * handles sector/page erasure if required, and manages any platform-specific constraints.
 *
 * @param[in] data  Pointer to the data buffer to be written.
 * @param[in] len   Length of the data buffer in bytes.
 *
 * @return int32_t  Returns 0 on success, or a negative error code on failure.
 *
 * @note Ensure that flash is initialized before calling this function. Some flash devices
 *       require that sectors be erased before writing. Behavior may vary based on
 *       platform and flash type (internal/external).
 */
int32_t write_data_to_flash(const uint8_t* data, uint32_t len) {
    FILE* fs = NULL;
    uint32_t total = 0;
    uint32_t used = 0;
    get_total_flash_size(&total, &used);
    fs = fopen(filepath, "ab");
    if(fs == NULL) {
        ESP_LOGE("FLASH HANDLER: ", "Error in Writing");
        return -1;
    }
    if(used < total) {
        fwrite(data, sizeof(uint8_t), len, fs);
    }
    fflush(fs);  // optional but safer
    fclose(fs);
    return 0;
}

void write_data_to_nexus(void* param) {
    ESP_LOGI("NEXUS_HANDLE: ", "TASK CREATED");
    file_event_queue = xQueueCreate(1, sizeof(1));
    RTOSNotifyIndex fw_file_xfer_state;
    while (1) {
        if (xQueueReceive(file_event_queue, &fw_file_xfer_state, pdMS_TO_TICKS(10)) == pdTRUE) {
            ESP_LOGI("FW_HAN: ", "%d", fw_file_xfer_state);
            switch (fw_file_xfer_state) {
            case FW_CHUNK_START:
                ESP_LOGI("FW_XFER: ", "BLE_FW_CHUNK_START");
                send_start_file_transfer();
                break;
            case FW_CHUNK_DATA:
                ESP_LOGI("FW_XFER: ", "BLE_FW_CHUNK_DATA");
                send_data_to_nexus();
                break;
            case FW_CHUNK_END:
                ESP_LOGI("FW_XFER: ", "BLE_FW_CHUNK_END");
                fw_file_xfer_state = BLE_FW_CHUNK_START;
                break;
            default:
                break;
            }
        }
        vTaskDelay(pdMS_TO_TICKS(10));
    }
}

void send_start_file_transfer(void) {
    uint32_t timer_counter = 0;
    /* SEND START BYTE AND WAIT FOR RESPONSE FROM NEXUS */
    memset(write_file_buffer, '\0', 10);
    write_file_buffer[START_BYTE_INDEX] = START_BYTE;
    write_file_buffer[PACKET_TYPE_INDEX] = BLE_FW_CHUNK_START;
    u32_to_byte_array_little_endian(&write_file_buffer[LENGTH_START_INDEX], sizeof(uint32_t));

    uint32_t len =  get_file_size();
    u32_to_byte_array_little_endian(&write_file_buffer[HEADER_SIZE], len);

    uint32_t calc_crc = crc32(write_file_buffer, HEADER_SIZE+sizeof(uint32_t));
    u32_to_byte_array_little_endian(&write_file_buffer[HEADER_SIZE+sizeof(uint32_t)], calc_crc);

    write_data((const char *)write_file_buffer, (HEADER_SIZE + sizeof(uint32_t) + CRC_LENGTH));
    vTaskDelay(pdMS_TO_TICKS(10));
    ack_flag = false;
    // 5 MINUTES HANDLER
    while (1) {
        vTaskDelay(pdMS_TO_TICKS(1000));
        if ((ack_flag == true) || timer_counter > 300 ) {
            break;
        }
        timer_counter++;
    }
    if (ack_flag == true) {
        timer_counter = 0;
        ack_flag = false;
        send_data_to_nexus();
    }
    else {
        ESP_LOGE("ERROR", "Exiting Start File Transfer");
    }
}

int32_t send_offset_data_to_nexus(void) {
    FILE* fs = NULL;
    uint32_t timer_counter = 0;
    fs = fopen("/storage/nexus_fw.bin", "rb"); 
    if(fs == NULL) {
        ESP_LOGE("FLASH HANDLER: ", "Error in Reading");
        return FILE_CORRUPTION_ERROR;
    }
    size_t bytes_read = 0;
    int chunk_index = 0;
    uint32_t file_length = 0;
    // Move file pointer 10 bytes from beginning
    if (fseek(fs, get_fw_file_offset(), SEEK_SET) == 0) {
        /* SEND CHUNKS OF 256KB TILL END */
        while ((bytes_read = fread(&write_file_buffer[HEADER_SIZE], 1, CHUNK_SIZE, fs)) > 0) {
            write_file_buffer[0] = START_BYTE;
            write_file_buffer[1] = BLE_FW_CHUNK_DATA;
            u32_to_byte_array_little_endian(&write_file_buffer[LENGTH_START_INDEX], bytes_read);
            uint32_t calc_crc = crc32(write_file_buffer, HEADER_SIZE + bytes_read);
            u32_to_byte_array_little_endian(&write_file_buffer[HEADER_SIZE + bytes_read], calc_crc);

            ESP_LOGI("UART", "Sending chunk %d (%u bytes)", chunk_index++, (unsigned)bytes_read);

            write_data((const char *)write_file_buffer, HEADER_SIZE + bytes_read + CRC_LENGTH);
            vTaskDelay(pdMS_TO_TICKS(10));
            file_length += bytes_read;
            if(file_length >= get_fw_file_length()) {
                break;
            }
            while (1) {
                vTaskDelay(pdMS_TO_TICKS(1000));
                if ((ack_flag == true) || timer_counter > 300 ) {
                    break;
                }
                timer_counter++;
            }
            if (ack_flag == true) {
                timer_counter = 0;
                ack_flag = false;
            }
            else {
                ESP_LOGE("ERROR", "Exiting File Transfer");
                break;
            }
            
            ESP_LOGI("UART", "ACK received for chunk %d", chunk_index - 1);
        }
        fclose(fs);
        }
        else {
            ESP_LOGE("FLASH HANDLER: ", "Fseek Error in Reading");
            fclose(fs); //close file handle on fseek error
        }
    return 0;
} 

/**
 * @brief Sends prepared data to the Nexus device.
 *
 * This function handles the transmission of data to the Nexus over a predefined
 * communication interface (e.g., UART, SPI, I2C). It may include framing, checksum
 * calculation, and retries as per the Nexus protocol specification.
 *
 * @return int32_t Returns 0 on successful transmission, or a negative error code on failure.
 *
 * @note Ensure that the communication interface with Nexus is properly initialized
 *       before calling this function.
 */
int32_t send_data_to_nexus(void) {
    FILE* fs = NULL;
    uint32_t timer_counter = 0;
    fs = fopen("/storage/nexus_fw.bin", "rb"); 
    if(fs == NULL) {
        ESP_LOGE("FLASH HANDLER: ", "Error in Reading");
        return FILE_CORRUPTION_ERROR;
    }
    size_t bytes_read = 0;
    int chunk_index = 0;

    /* SEND CHUNKS OF 256KB TILL END */
    while ((bytes_read = fread(&write_file_buffer[HEADER_SIZE], 1, CHUNK_SIZE, fs)) > 0) {
        write_file_buffer[0] = START_BYTE;
        write_file_buffer[1] = BLE_FW_CHUNK_DATA;
        u32_to_byte_array_little_endian(&write_file_buffer[LENGTH_START_INDEX], bytes_read);
        uint32_t calc_crc = crc32(write_file_buffer, HEADER_SIZE + bytes_read);
        u32_to_byte_array_little_endian(&write_file_buffer[HEADER_SIZE + bytes_read], calc_crc);

        ESP_LOGI("UART", "Sending chunk %d (%u bytes)", chunk_index++, (unsigned)bytes_read);

        write_data((const char *)write_file_buffer, HEADER_SIZE + bytes_read + CRC_LENGTH);
        vTaskDelay(pdMS_TO_TICKS(10));
        while (1) {
            vTaskDelay(pdMS_TO_TICKS(1000));
            if ((ack_flag == true) || timer_counter > 300 ) {
                break;
            }
            timer_counter++;
        }
        if (ack_flag == true) {
            timer_counter = 0;
            ack_flag = false;
        }
        else {
            ESP_LOGE("ERROR", "Exiting File Transfer");
            break;
        }
        
        ESP_LOGI("UART", "ACK received for chunk %d", chunk_index - 1);
    }
    fclose(fs);
    return 0;
}

/**
 * @brief Erases or formats the entire flash memory.
 *
 * This function performs a complete flash memory erase operation, effectively 
 * formatting the flash by resetting all sectors/pages to their default erased state.
 * It may be used to prepare the flash for fresh data storage.
 *
 * @return int32_t Returns 0 on success, or a negative error code on failure.
 *
 * @note This operation is destructive — all existing data in the flash will be lost.
 *       Ensure the flash interface is properly initialized before calling this function.
 */
int32_t format_flash(void) {
    uint32_t total, used;
    esp_err_t result = ESP_OK;
    get_total_flash_size(&total, &used);
    ESP_LOGI("FLASH: ", "Before Formating: Total: %ld, Used: %ld", total, used);
    result = esp_littlefs_format(config.partition_label);
    if (result != ESP_OK){
        ESP_LOGE("FLASH HANDLER: ", "Formating Failed...");
    }    
    get_total_flash_size(&total, &used);
    ESP_LOGI("FLASH: ", "After Formating: Total: %ld, Used: %ld", total, used);
    current_file_length = 0;
    return (int32_t)result;
}

/**
 * @brief Callback invoked when a data packet is received.
 *
 * This function is called when a new packet is successfully received from the
 * communication interface. It can be used to trigger further processing,
 * buffering, or acknowledgment of the received data.
 *
 * @param[in] len  Length of the received packet in bytes.
 *
 * @return void
 */
void current_packet_received(uint32_t len) {
    current_file_length += len;
}

/**
 * @brief Retrieves the size of the current file.
 *
 * This function returns the size of a file currently being processed or accessed.
 * It may be used to determine how much data needs to be read, transmitted, or written.
 *
 * @return uint32_t  Size of the file in bytes.
 */
uint32_t get_file_size(void) {
    struct stat st;

    if (stat(filepath, &st) == 0) {
        ESP_LOGI("FILE", "Size of '%s' is %ld bytes", filepath, st.st_size);
        ESP_LOGI("FILE", "Loss: %ld bytes", (current_file_length - st.st_size));
        return st.st_size;

    } else {
        ESP_LOGE("FILE", "Failed to get size of '%s'", filepath);
        return 0;
    }
}
